# 项目上下文信息

- AI模拟面试功能重构需求：新增开始按钮和实时对话流程，使用LLM实现面试官-用户问答循环，严格保持现有UI不变
- 用户要求输出git提交命令进行手动提交，需要使用"开发版："前缀，并完整记录当前的改动内容。
- 后端服务器启动问题解决方案：当前端出现500错误且无法连接API时，首先检查后端服务器是否在运行。使用 `netstat -ano | findstr ":3000" | findstr "LISTENING"` 检查端口状态，如果没有LISTENING状态则需要启动后端服务器。在backend目录下使用 `npx tsx watch server.ts` 启动开发服务器，确保数据库连接和WebSocket服务正常初始化。
- 修复分享有礼页面邀请码和邀请链接复制功能Toast提示不显示问题：将ReferralRewardsPage.tsx中的useToast hook改为useToastContext，修复了Toast状态隔离导致的显示问题，现在复制按钮点击后能正常显示绿色成功Toast提示
- 修复管理系统Toast进度条问题：1.修复ToastContext.tsx中ToastContainer缺少toasts和onRemove参数传递的问题 2.将Toast.tsx中复杂的JavaScript+requestAnimationFrame进度条实现改为简单可靠的CSS动画方式，与主系统保持一致
- 通知系统测试已完成：管理后台成功创建通知"测试通知功能"，通知被正确保存到数据库，前端成功从API获取并显示通知列表，createUserNotifications函数被正确调用。用户已登录主系统准备测试通知接收功能。
- 通知管理系统开发完成：实现了完整的管理后台通知CRUD功能、用户通知接收机制、主系统通知显示界面，包括数据库schema设计(Notification/NotificationTarget/UserNotification表)、后端API路由、前端管理界面和通知下拉组件，已通过完整测试验证功能正常
- AI模拟面试添加15秒倒计时功能：位置1在开始面试按钮"正在启动..."旁显示倒计时，位置2在实时页面等待文案处显示倒计时，纯前端UI优化不影响后端逻辑，文案改为"面试君正在分析您的简历，为您量身定制专业面试问题... 请稍等片刻，精彩的面试体验即将开始！"
- AI模拟面试倒计时功能完成：位置1开始面试按钮倒计时10秒，位置2实时页面倒计时15秒，输入栏高度调整为原来1/3，文案优化为"面试君正在分析您的简历，为您量身定制专业面试问题... 请稍等片刻，精彩的面试体验即将开始！"
- 用户在PricingPage.tsx中修复了充值中心页面的UI对齐问题，将上下两个白色卡片的宽度从max-w-2xl调整为max-w-xl，确保"面巾购买"卡片和"兑换规则"卡片的左右边缘完全对齐
- 完成模拟面试系统真实岗位数据连接功能：修改backend/websocket/handlers/mockInterviewService.ts，在startMockInterview方法中添加数据库更新逻辑，新增updateSessionJobInfo私有方法，解决"未知公司"和"Job Info from Client"模拟数据问题，实现前端岗位选择到数据库titleJobInfo字段的完整数据流连接
- 完成AI正式面试岗位信息连接修复：1)后端添加sessionManager.ts的updateSessionJobInfo方法和messageHandler.ts的update_job_info消息处理；2)前端修改useInterviewSession.ts添加sendTextMessage方法，LiveInterviewPage.tsx添加WebSocket岗位信息发送逻辑；3)修复面试记录时间显示问题，将UTC时间转换为Asia/Shanghai本地时间；4)解决AI正式面试"未知公司"显示问题，实现真实岗位数据传递
- MianshiJun项目已成功部署到生产服务器121.40.57.218。后端服务通过PM2运行在端口3000，前端通过Nginx在端口80提供服务。API代理和WebSocket代理已配置。用户希望配置SSL证书以启用HTTPS。
- MianshiJun项目已完全部署完成，包括HTTPS SSL证书配置。网站可通过 https://mianshijun.xyz 访问，所有服务正常运行，HTTP自动重定向到HTTPS，证书自动续期已配置。
- 服务器部署问题：backend/routes/auth.ts中VerificationService导入路径大小写错误，需要从'../services/VerificationService'改为'../services/verificationService'
- 遇到部署问题时，一律1.本地修复 → 2. 推送GitHub → 3. 服务器拉取。- 项目是monorepo结构，包含packages/common包。common包是私有包，需要在服务器上配置workspace或构建后上传。前端依赖@new-mianshijun/common导致npm install失败。
- 本次更新完善了邀请码系统的用户体验和功能完整性：1.修复showToast错误，统一使用Toast组件；2.修复邀请码生成包含特殊字符问题，使用纯数字字母；3.优化邀请人邀请码填写UI，保持布局不变，从输入状态转换为显示状态；4.增强后端API返回邀请人邀请码信息；5.修复兑换功能事务超时问题
- 修复邀请记录卡片滚动功能：设置max-h-[290px]限制高度，确保左右卡片高度一致，当邀请记录超过2-3条时自动显示滚动条，解决了内容溢出和高度不一致问题
