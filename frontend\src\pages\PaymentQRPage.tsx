import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, CheckCircle, XCircle, Clock, RefreshCw } from 'lucide-react';
import useDocumentTitle from '../hooks/useDocumentTitle';

const PaymentQRPage: React.FC = () => {
  useDocumentTitle('支付确认');
  
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'success' | 'failed'>('pending');
  const [countdown, setCountdown] = useState(300); // 5分钟倒计时

  // 从URL参数获取订单信息
  const orderId = searchParams.get('orderId');
  const amount = searchParams.get('amount');
  const itemDescription = searchParams.get('itemDescription');
  const paymentMethod = searchParams.get('paymentMethod');
  const qrCodeUrl = searchParams.get('qrCodeUrl');
  const qrImageUrl = searchParams.get('qrImageUrl');

  // 获取订单详情（包含二维码信息）
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [isLoadingOrder, setIsLoadingOrder] = useState(true);

  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!orderId) return;

      try {
        const response = await fetch(`/api/orders/${orderId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setOrderDetails(data.order);
          }
        }
      } catch (error) {
        console.error('获取订单详情失败:', error);
      } finally {
        setIsLoadingOrder(false);
      }
    };

    fetchOrderDetails();
  }, [orderId]);

  // 倒计时效果
  useEffect(() => {
    if (paymentStatus !== 'pending') return;

    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          setPaymentStatus('failed');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [paymentStatus]);

  // 格式化倒计时显示
  const formatCountdown = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 检查支付状态
  const checkPaymentStatus = async () => {
    if (!orderId) return;

    try {
      const response = await fetch(`/api/payments/status/${orderId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          if (data.status === 'COMPLETED') {
            setPaymentStatus('success');
          } else if (data.status === 'FAILED') {
            setPaymentStatus('failed');
          }
          // PENDING状态保持不变，继续等待
        }
      }
    } catch (error) {
      console.error('检查支付状态失败:', error);
    }
  };

  // 返回上一页
  const handleGoBack = () => {
    navigate(-1);
  };

  // 重新支付
  const handleRetryPayment = () => {
    setPaymentStatus('pending');
    setCountdown(300);
  };

  // 如果没有必要的参数，返回错误页面
  if (!orderId || !amount || !paymentMethod) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4">
        <div className="bg-white bg-opacity-90 backdrop-blur-md rounded-xl shadow-xl p-8 text-center">
          <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-xl font-bold text-gray-800 mb-2">支付信息错误</h1>
          <p className="text-gray-600 mb-6">缺少必要的支付参数，请重新发起支付</p>
          <button
            onClick={handleGoBack}
            className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all"
          >
            返回上一页
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-3">
      <div className="max-w-sm mx-auto">
        {/* 头部导航 */}
        <div className="flex items-center gap-3 mb-4 pt-2">
          <button
            onClick={handleGoBack}
            className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <h1 className="text-lg font-bold text-gray-800">支付确认</h1>
        </div>

        {/* 支付状态卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white bg-opacity-90 backdrop-blur-md rounded-xl shadow-xl overflow-hidden"
        >
          {/* 订单信息 */}
          <div className="p-4 border-b border-gray-100">
            <h2 className="text-base font-semibold text-gray-800 mb-3">订单详情</h2>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">商品名称</span>
                <span className="text-gray-800 font-medium">{itemDescription}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">订单编号</span>
                <span className="text-gray-800 font-mono text-xs">{orderId}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">创建时间</span>
                <span className="text-gray-800 text-xs">
                  {orderDetails?.createdAt ? new Date(orderDetails.createdAt).toLocaleString('zh-CN') : '加载中...'}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">支付方式</span>
                <span className="text-gray-800">
                  {paymentMethod === 'ALIPAY' ? '支付宝' : '微信支付'}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">订单状态</span>
                <span className="text-orange-600 font-medium">
                  {paymentStatus === 'pending' ? '待支付' :
                   paymentStatus === 'success' ? '已支付' : '支付失败'}
                </span>
              </div>
              <div className="flex justify-between text-base font-semibold pt-2 border-t border-gray-100">
                <span className="text-gray-800">支付金额</span>
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 text-lg">
                  ¥{amount}
                </span>
              </div>
            </div>
          </div>

          {/* 支付状态区域 */}
          <div className="p-4">
            {paymentStatus === 'pending' && (
              <div className="text-center">
                {/* 二维码显示区域 */}
                <div className="w-48 h-48 mx-auto mb-4 bg-white rounded-lg border border-gray-200 flex items-center justify-center">
                  {qrImageUrl ? (
                    <img
                      src={qrImageUrl}
                      alt="支付二维码"
                      className="w-44 h-44 object-contain"
                      onError={(e) => {
                        // 如果图片加载失败，显示备用二维码
                        console.error('二维码图片加载失败');
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  ) : isLoadingOrder ? (
                    <div className="text-center">
                      <RefreshCw className="h-8 w-8 text-blue-500 mx-auto mb-2 animate-spin" />
                      <p className="text-xs text-gray-500">正在生成二维码...</p>
                    </div>
                  ) : (
                    <div className="text-center">
                      <div className="w-32 h-32 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mb-2">
                        <span className="text-white text-xs">支付二维码</span>
                      </div>
                      <p className="text-xs text-gray-500">请使用{paymentMethod === 'ALIPAY' ? '支付宝' : '微信'}扫码支付</p>
                    </div>
                  )}
                </div>

                {/* 支付提示信息 */}
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-800 mb-1">
                    请使用{paymentMethod === 'ALIPAY' ? '支付宝' : '微信'}扫码支付
                  </p>
                  <p className="text-xs text-gray-600">
                    扫码后请在手机上完成支付
                  </p>
                </div>

                {/* 倒计时 */}
                <div className="flex items-center justify-center gap-2 mb-4">
                  <Clock className="h-4 w-4 text-orange-500" />
                  <span className="text-orange-600 font-medium text-sm">
                    剩余时间: {formatCountdown(countdown)}
                  </span>
                </div>

                {/* 检查支付状态按钮 */}
                <button
                  onClick={checkPaymentStatus}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-2.5 rounded-lg font-medium hover:shadow-lg transition-all flex items-center justify-center gap-2 text-sm"
                >
                  <RefreshCw className="h-4 w-4" />
                  检查支付状态
                </button>
              </div>
            )}

            {paymentStatus === 'success' && (
              <div className="text-center">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-3" />
                <h3 className="text-lg font-bold text-gray-800 mb-2">支付成功</h3>
                <p className="text-gray-600 mb-4 text-sm">您的订单已支付成功，相关服务已到账</p>
                <button
                  onClick={() => navigate('/pricing')}
                  className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-2.5 rounded-lg font-medium hover:shadow-lg transition-all text-sm"
                >
                  返回充值中心
                </button>
              </div>
            )}

            {paymentStatus === 'failed' && (
              <div className="text-center">
                <XCircle className="h-12 w-12 text-red-500 mx-auto mb-3" />
                <h3 className="text-lg font-bold text-gray-800 mb-2">支付失败</h3>
                <p className="text-gray-600 mb-4 text-sm">支付超时或取消，请重新发起支付</p>
                <div className="space-y-2">
                  <button
                    onClick={handleRetryPayment}
                    className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-2.5 rounded-lg font-medium hover:shadow-lg transition-all text-sm"
                  >
                    重新支付
                  </button>
                  <button
                    onClick={handleGoBack}
                    className="w-full border border-gray-300 text-gray-700 py-2.5 rounded-lg font-medium hover:bg-gray-50 transition-all text-sm"
                  >
                    返回上一页
                  </button>
                </div>
              </div>
            )}
          </div>
        </motion.div>

        {/* 支付说明 */}
        <div className="mt-4 bg-white bg-opacity-70 backdrop-blur-md rounded-xl p-3">
          <h4 className="font-medium text-gray-800 mb-2 text-sm">支付说明</h4>
          <ul className="text-xs text-gray-600 space-y-0.5">
            <li>• 请在规定时间内完成支付</li>
            <li>• 支付成功后服务将立即到账</li>
            <li>• 如遇问题请联系客服</li>
            <li>• 支付过程中请勿关闭页面</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default PaymentQRPage;
