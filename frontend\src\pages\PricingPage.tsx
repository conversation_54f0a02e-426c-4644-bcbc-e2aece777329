// 文件路径: frontend/src/pages/PricingPage.tsx
// (File Path: frontend/src/pages/PricingPage.tsx)
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import PackageCard from '../components/Pricing/PackageCard';
import PointsPackage from '../components/Pricing/PointsPackage';
import TabNavigation from '../components/Pricing/TabNavigation';
import PaymentModal from '../components/ui/PaymentModal';
import { CheckCircle, Gift, Repeat } from 'lucide-react'; // Added Repeat for exchange icon
import useDocumentTitle from '../hooks/useDocumentTitle';
import { createOrder } from '../lib/api/paymentService';
import { useToastContext } from '../contexts/ToastContext';
import { redeemCodeApi } from '../lib/api/redeem';
import { exchangeCredits } from '../lib/api/exchange';
import useUserBalanceStore from '../stores/userBalanceStore';

const PricingPage: React.FC = () => {
  // 设置页面标题
  useDocumentTitle('充值中心');

  const navigate = useNavigate();
  const { showSuccess, showError } = useToastContext();
  const { fetchBalance } = useUserBalanceStore();

  const [activeTab, setActiveTab] = useState('套餐礼包'); // '套餐礼包' or '面巾值'
  const [selectedPackageId, setSelectedPackageId] = useState<string | null>(null);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedPackageForPayment, setSelectedPackageForPayment] = useState<any>(null);
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);

  // 兑换码相关状态
  const [redeemCode, setRedeemCode] = useState('');
  const [isRedeeming, setIsRedeeming] = useState(false);

  // 面巾兑换相关状态
  const [isExchanging, setIsExchanging] = useState<{ [key: string]: boolean }>({});

  const tabs = ['套餐礼包', '面巾值', '兑换码兑换'];

  const packagesData = [
    {
      id: 'basic',
      title: '基础礼包',
      description: '体验AI面试的智能力量',
      price: 68,
      features: ['AI模拟面试2次', 'AI正式面试2次', '附赠100面巾'],
      notes: ['智能题库实时更新', 'AI深度分析报告'],
      color: 'bg-gradient-to-br from-gray-50 to-gray-100',
      borderColor: 'border-gray-200',
      popular: false,
      buyButtonText: '立即购买',
    },
    {
      id: 'advanced',
      title: '高级礼包',
      description: '75%用户的选择，更多面试机会', // Updated description
      price: 138,
      features: ['AI模拟面试4次', 'AI正式面试4次', '附赠400面巾'],
      notes: ['智能题库实时更新', 'AI深度分析报告', '面试时长无忧'], // Updated notes
      color: 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50', // More vibrant gradient
      borderColor: 'border-blue-300',
      popular: true,
      popularText: '智能推荐全能版，更多面试机会',
      buyButtonText: '立即购买',
    },
    {
      id: 'super',
      title: '尊享礼包',
      description: '为重要面试做好充分准备', // Updated description
      price: 298,
      features: ['AI模拟面试10次', 'AI正式面试8次', '附赠800面巾'],
      notes: ['智能题库实时更新', 'AI深度分析报告', '专属客服支持'], // Updated notes
      color: 'bg-gradient-to-br from-gray-50 to-gray-100',
      borderColor: 'border-gray-200',
      popular: false,
      buyButtonText: '立即购买',
    },
  ];

  const pointsPackagesData = [
    { id: 'points_100', points: 100, price: 30, buyButtonText: '购买' },
    { id: 'points_200', points: 200, price: 60, buyButtonText: '购买' },
    { id: 'points_1000', points: 1000, price: 90, buyButtonText: '购买' }, // Corrected price as per task
  ];

  const redemptionRules = [
    { amount: 100, description: 'AI模拟面试 (1次)'},
    { amount: 200, description: 'AI正式面试 (1次)'}
  ];

  // 处理套餐选中
  const handlePackageSelect = (packageId: string) => {
    setSelectedPackageId(selectedPackageId === packageId ? null : packageId);
  };

  // 处理购买 - 打开支付方式选择弹窗
  const handlePurchase = (pkg: any) => {
    setSelectedPackageForPayment(pkg);
    setIsPaymentModalOpen(true);
  };

  // 处理支付方式选择
  const handlePaymentMethodSelect = async (paymentMethod: 'ALIPAY' | 'WECHATPAY') => {
    if (!selectedPackageForPayment) return;

    setIsCreatingOrder(true);
    try {
      // 创建订单
      const orderResponse = await createOrder({
        itemId: selectedPackageForPayment.id,
        amount: selectedPackageForPayment.price,
        paymentMethod: paymentMethod,
        itemDescription: selectedPackageForPayment.title
      });

      // 关闭支付弹窗
      setIsPaymentModalOpen(false);

      // 跳转到支付二维码页面
      const searchParams = new URLSearchParams({
        orderId: orderResponse.orderId,
        amount: selectedPackageForPayment.price.toString(),
        itemDescription: selectedPackageForPayment.title,
        paymentMethod: paymentMethod
      });

      navigate(`/payment-qr?${searchParams.toString()}`);

      showSuccess('订单创建成功，请完成支付');
    } catch (error: any) {
      showError(error.message || '创建订单失败，请稍后再试');
    } finally {
      setIsCreatingOrder(false);
    }
  };

  // 关闭支付弹窗
  const handleClosePaymentModal = () => {
    setIsPaymentModalOpen(false);
    setSelectedPackageForPayment(null);
  };

  // 处理兑换码兑换
  const handleRedeemCode = async () => {
    if (!redeemCode.trim()) {
      showError('请输入兑换码');
      return;
    }

    setIsRedeeming(true);
    try {
      const response = await redeemCodeApi(redeemCode.trim());

      // 根据兑换类型显示不同的成功提示
      let successMessage = '';
      if (response.benefitType === 'POINTS') {
        successMessage = `兑换成功！获得 ${response.benefitValue} 面巾，当前余额：${response.newBalance}`;
      } else if (response.benefitType === 'MOCK_INTERVIEW') {
        successMessage = `兑换成功！获得 ${response.benefitValue} 次AI模拟面试机会`;
      } else if (response.benefitType === 'FORMAL_INTERVIEW') {
        successMessage = `兑换成功！获得 ${response.benefitValue} 次AI正式面试机会`;
      } else {
        // 默认提示
        successMessage = `兑换成功！获得 ${response.benefitValue} 个奖励`;
      }

      showSuccess(successMessage);
      setRedeemCode(''); // 清空输入框

      // 兑换成功后刷新用户余额数据
      await fetchBalance();
    } catch (error: any) {
      showError(error.message || '兑换失败，请检查兑换码或稍后再试');
    } finally {
      setIsRedeeming(false);
    }
  };

  // 处理面巾兑换
  const handleExchange = async (type: 'mock' | 'formal') => {
    const exchangeKey = `exchange_${type}`;

    setIsExchanging(prev => ({ ...prev, [exchangeKey]: true }));
    try {
      const response = await exchangeCredits(type);

      if (response.success) {
        showSuccess(response.message);
        // 兑换成功后刷新用户余额数据
        await fetchBalance();
      } else {
        showError(response.message || '兑换失败，请稍后再试');
      }
    } catch (error: any) {
      showError(error.message || '兑换失败，请稍后再试');
    } finally {
      setIsExchanging(prev => ({ ...prev, [exchangeKey]: false }));
    }
  };

  return (
    <div className="h-screen flex flex-col px-6 py-2">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-3"
      >
        <h1 className="text-3xl font-extrabold text-gray-800 mb-1">
          充值中心
        </h1>
        <p className="text-sm text-gray-600 max-w-2xl mx-auto">
          选择适合您的面试提升方案，或按需充值面巾，自由兑换各项服务。
        </p>
      </motion.div>

      <TabNavigation tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />

      {activeTab === '套餐礼包' && (
        <motion.div
          key="packages"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-3 mb-2 flex-1"
        >
          {packagesData.map((pkg) => (
            <PackageCard
              key={pkg.id}
              pkg={pkg}
              isSelected={selectedPackageId === pkg.id}
              onSelect={handlePackageSelect}
              onPurchase={handlePurchase}
            />
          ))}
        </motion.div>
      )}

      {activeTab === '面巾值' && (
        <motion.div
          key="points"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="mb-4 flex-1"
        >
          <div className="bg-white bg-opacity-70 backdrop-blur-md p-6 rounded-xl shadow-xl border border-gray-200 max-w-xl mx-auto h-[365px]">
            <h2 className="text-xl font-bold text-gray-800 text-center mb-4">面巾购买</h2>
            <div className="space-y-3">
              {pointsPackagesData.map((pkg) => (
                <PointsPackage key={pkg.id} pkg={pkg} onPurchase={handlePurchase} />
              ))}
            </div>
          </div>
        </motion.div>
      )}

      {activeTab === '兑换码兑换' && (
        <motion.div
          key="redeem"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="max-w-2xl mx-auto mb-4 flex-1 flex items-center justify-center"
        >
          <div className="bg-white bg-opacity-70 backdrop-blur-md p-6 rounded-xl shadow-xl border border-gray-200 w-[514.35px] h-[315px]">
            <h2 className="text-xl font-bold text-gray-800 text-center mb-4">兑换码兑换</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  请输入兑换码
                </label>
                <input
                  type="text"
                  value={redeemCode}
                  onChange={(e) => setRedeemCode(e.target.value)}
                  placeholder="请输入您的兑换码"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-center text-lg tracking-wider"
                  disabled={isRedeeming}
                />
              </div>
              <button
                onClick={handleRedeemCode}
                disabled={isRedeeming || !redeemCode.trim()}
                className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {isRedeeming ? '兑换中...' : '立即兑换'}
              </button>
              <div className="text-center text-sm text-gray-500 mt-4">
                <p>• 兑换码仅限使用一次</p>
                <p>• 兑换成功后面巾将自动到账</p>
                <p>• 如有问题请联系客服</p>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* 套餐礼包页面的兑换规则 - 使用原始宽度 */}
      {activeTab === '套餐礼包' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="bg-white bg-opacity-70 backdrop-blur-md p-4 rounded-xl shadow-xl border border-gray-200 mt-auto"
        >
          <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2 justify-center">
            <Repeat className="h-5 w-5 text-blue-600" />
            兑换规则
          </h3>
          <div className="grid md:grid-cols-2 gap-x-6 gap-y-3 max-w-xl mx-auto">
            {redemptionRules.map((rule, index) => (
               <div key={index} className="flex items-center gap-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white flex items-center justify-center font-bold text-sm shadow-md">
                  {rule.amount}
                </div>
                <div>
                  <p className="font-semibold text-gray-700 text-sm">{rule.amount} 面巾</p>
                  <p className="text-xs text-gray-600">{rule.description}</p>
                </div>
              </div>
            ))}
          </div>
          <p className="text-center text-xs text-gray-500 mt-3">
            * 面巾是面试君平台虚拟道具，可用于兑换平台内指定服务。最终解释权归面试君所有。
          </p>
        </motion.div>
      )}

      {/* 面巾值页面的兑换规则 - 使用与面巾购买一致的宽度 */}
      {activeTab === '面巾值' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="bg-white bg-opacity-70 backdrop-blur-md p-6 rounded-xl shadow-xl border border-gray-200 mt-auto max-w-xl mx-auto"
        >
          <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2 justify-center">
            <Repeat className="h-5 w-5 text-blue-600" />
            兑换规则
          </h3>
          <div className="grid md:grid-cols-2 gap-x-6 gap-y-3">
            {redemptionRules.map((rule, index) => {
              const exchangeType = rule.amount === 100 ? 'mock' : 'formal';
              const exchangeKey = `exchange_${exchangeType}`;
              const isExchangingThis = isExchanging[exchangeKey] || false;

              return (
                <div key={index} className="flex items-center justify-between gap-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white flex items-center justify-center font-bold text-sm shadow-md">
                      {rule.amount}
                    </div>
                    <div>
                      <p className="font-semibold text-gray-700 text-sm">{rule.amount} 面巾</p>
                      <p className="text-xs text-gray-600">{rule.description}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleExchange(exchangeType)}
                    disabled={isExchangingThis}
                    className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 py-1.5 rounded-lg text-xs font-medium hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none whitespace-nowrap"
                  >
                    {isExchangingThis ? '兑换中...' : '立即兑换'}
                  </button>
                </div>
              );
            })}
          </div>
          <p className="text-center text-xs text-gray-500 mt-3">
            * 面巾是面试君平台虚拟道具，可用于兑换平台内指定服务。最终解释权归面试君所有。
          </p>
        </motion.div>
      )}

      {/* 兑换码兑换页面的兑换规则 - 使用与兑换码输入区域一致的宽度 */}
      {activeTab === '兑换码兑换' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="bg-white bg-opacity-70 backdrop-blur-md p-6 rounded-xl shadow-xl border border-gray-200 mt-auto max-w-2xl mx-auto"
        >
          <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2 justify-center">
            <Repeat className="h-5 w-5 text-blue-600" />
            兑换规则
          </h3>
          <div className="grid md:grid-cols-2 gap-x-6 gap-y-3">
            {redemptionRules.map((rule, index) => (
               <div key={index} className="flex items-center gap-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white flex items-center justify-center font-bold text-sm shadow-md">
                  {rule.amount}
                </div>
                <div>
                  <p className="font-semibold text-gray-700 text-sm">{rule.amount} 面巾</p>
                  <p className="text-xs text-gray-600">{rule.description}</p>
                </div>
              </div>
            ))}
          </div>
          <p className="text-center text-xs text-gray-500 mt-3">
            * 面巾是面试君平台虚拟道具，可用于兑换平台内指定服务。最终解释权归面试君所有。
          </p>
        </motion.div>
      )}

      {/* 支付方式选择弹窗 */}
      {selectedPackageForPayment && (
        <PaymentModal
          isOpen={isPaymentModalOpen}
          onClose={handleClosePaymentModal}
          onPaymentMethodSelect={handlePaymentMethodSelect}
          packageInfo={{
            title: selectedPackageForPayment.title,
            price: selectedPackageForPayment.price,
            description: selectedPackageForPayment.description
          }}
        />
      )}
    </div>
  );
};

export default PricingPage;
