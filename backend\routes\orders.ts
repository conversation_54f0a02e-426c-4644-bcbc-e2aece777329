import express, { Request, Response } from 'express';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import prisma from '../lib/prisma';
import AlipayService from '../services/alipayService';

const router = express.Router();

/**
 * 创建订单
 */
router.post('/create', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { itemId, amount, paymentMethod, itemDescription } = req.body;

    // 验证必填字段
    if (!itemId || !amount || !paymentMethod) {
      return res.status(400).json({ 
        success: false,
        message: '缺少必要的订单信息' 
      });
    }

    // 验证金额格式
    const orderAmount = parseFloat(amount);
    if (isNaN(orderAmount) || orderAmount <= 0) {
      return res.status(400).json({ 
        success: false,
        message: '订单金额无效' 
      });
    }

    // 验证支付方式
    if (!['ALIPAY', 'WECHATPAY'].includes(paymentMethod)) {
      return res.status(400).json({ 
        success: false,
        message: '不支持的支付方式' 
      });
    }

    // 创建订单
    const order = await prisma.order.create({
      data: {
        userId: userId,
        itemId: itemId,
        amount: orderAmount,
        paymentMethod: paymentMethod,
        itemDescription: itemDescription || '',
        status: 'PENDING',
      },
    });

    // 如果是支付宝支付，生成支付跳转URL
    let paymentInfo = null;
    if (paymentMethod === 'ALIPAY') {
      try {
        // 构建回调URL（需要根据实际部署环境调整）
        const baseUrl = process.env.NODE_ENV === 'production'
          ? 'https://mianshijun.xyz'
          : 'http://localhost:3000';

        const notifyUrl = `${baseUrl}/api/payments/alipay-notify`;
        const returnUrl = `${baseUrl}/payment-success?orderId=${order.id}`;

        // 生成支付跳转URL（页面跳转方式）
        const paymentUrl = AlipayService.generatePaymentUrl({
          name: itemDescription || '面试君充值',
          money: orderAmount.toString(),
          out_trade_no: order.id,
          notify_url: notifyUrl,
          return_url: returnUrl,
          param: `userId:${userId}`
        });

        console.log('💰 支付宝支付URL生成成功:', paymentUrl);

        paymentInfo = {
          paymentUrl: paymentUrl,
          redirectType: 'url' // 标识这是URL跳转方式
        };

      } catch (error: any) {
        console.error('❌ 生成支付URL失败:', error);

        // 删除已创建的订单
        await prisma.order.delete({ where: { id: order.id } });

        return res.status(500).json({
          success: false,
          message: error.message || '创建支付失败，请稍后再试'
        });
      }
    }

    return res.status(201).json({
      success: true,
      orderId: order.id,
      message: '订单创建成功',
      order: {
        id: order.id,
        amount: order.amount,
        status: order.status,
        paymentMethod: order.paymentMethod,
        itemDescription: order.itemDescription,
        createdAt: order.createdAt,
      },
      paymentInfo: paymentInfo
    });

  } catch (error: any) {
    console.error('创建订单失败:', error);
    return res.status(500).json({ 
      success: false,
      message: '创建订单失败，请稍后再试' 
    });
  }
});

/**
 * 获取用户订单列表
 */
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;

    const orders = await prisma.order.findMany({
      where: { userId: userId },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        amount: true,
        status: true,
        paymentMethod: true,
        itemDescription: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    return res.status(200).json({
      success: true,
      orders: orders
    });

  } catch (error: any) {
    console.error('获取订单列表失败:', error);
    return res.status(500).json({ 
      success: false,
      message: '获取订单列表失败，请稍后再试' 
    });
  }
});

/**
 * 获取单个订单详情
 */
router.get('/:orderId', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.userId;
    const { orderId } = req.params;

    if (!orderId) {
      return res.status(400).json({ 
        success: false,
        message: '订单ID无效' 
      });
    }

    const order = await prisma.order.findFirst({
      where: {
        id: orderId,
        userId: userId, // 确保用户只能查看自己的订单
      },
      select: {
        id: true,
        amount: true,
        status: true,
        paymentMethod: true,
        itemId: true,
        itemDescription: true,
        transactionId: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    if (!order) {
      return res.status(404).json({ 
        success: false,
        message: '订单不存在' 
      });
    }

    return res.status(200).json({
      success: true,
      order: order
    });

  } catch (error: any) {
    console.error('获取订单详情失败:', error);
    return res.status(500).json({ 
      success: false,
      message: '获取订单详情失败，请稍后再试' 
    });
  }
});

export default router;
